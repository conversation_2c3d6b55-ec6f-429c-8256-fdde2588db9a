# Dust.tt 聊天记录导出工具 - 使用指南

## 🎯 快速开始

### Windows 用户
1. 双击运行 `start_export.bat`
2. 按照提示操作即可

### macOS/Linux 用户
1. 打开终端
2. 运行 `./start_export.sh`
3. 按照提示操作即可

### 手动运行（所有系统）
```bash
python quick_start.py
```

## 📋 完整流程说明

### 第一步：准备工作
1. 确保已安装 Python（3.6 或更高版本）
2. 确保网络连接正常
3. 准备好您的 Dust.tt API 密钥（已预设）

### 第二步：查找工作区 ID
工具会自动帮您查找工作区 ID，您可以：
- 输入应用名称（如"笔记导出"）让工具自动生成候选 ID
- 手动输入已知的工作区 ID
- 批量测试多个可能的 ID

### 第三步：导出聊天记录
工具会自动：
- 搜索工作区中的所有对话
- 验证对话 ID 的有效性
- 导出每个对话的完整数据
- 生成对话摘要 CSV 文件

### 第四步：查看结果
导出完成后，您会得到：
- 一个包含所有导出文件的目录
- 对话摘要 CSV 文件（便于快速浏览）
- 每个对话的详细 JSON 文件
- 导出报告文件

## 📁 输出文件说明

### 目录结构
```
dust_export_20250127_143022/
├── conversations_summary_20250127_143022.csv  # 对话摘要
├── conversation_abc123.json                   # 对话详情
├── conversation_def456.json                   # 对话详情
├── ...
└── export_report.json                         # 导出报告
```

### CSV 摘要文件字段
- `conversation_id`: 对话唯一标识符
- `title`: 对话标题
- `created_at`: 创建时间
- `updated_at`: 最后更新时间
- `message_count`: 消息数量
- `participant_count`: 参与者数量
- `status`: 对话状态

### JSON 详情文件内容
- `conversation`: 对话基本信息和所有消息
- `events`: 对话事件流（如消息发送、编辑等）
- `exported_at`: 导出时间戳
- `exporter_version`: 工具版本

## 🔧 高级使用

### 单独运行各个工具

#### 1. 测试 API 连接
```bash
python test_api.py
```

#### 2. 查找工作区 ID
```bash
python find_workspace_id.py
```

#### 3. 导出特定工作区
```bash
python advanced_dust_exporter.py --workspace-id YOUR_WORKSPACE_ID
```

#### 4. 导出到指定目录
```bash
python advanced_dust_exporter.py --workspace-id YOUR_WORKSPACE_ID --output-dir my_export
```

### 自定义配置
如果需要修改 API 密钥或其他设置，可以编辑相应的 Python 文件。

## ❓ 常见问题

### Q: 如何获取工作区 ID？
A: 使用我们提供的 `find_workspace_id.py` 工具，它会自动帮您查找。您也可以：
1. 在 Dust.tt 网页 URL 中查找（格式：`https://dust.tt/w/{工作区ID}/...`）
2. 使用浏览器开发者工具查看网络请求
3. 联系 Dust.tt 支持

### Q: 导出失败怎么办？
A: 请检查：
1. 网络连接是否正常
2. API 密钥是否有效
3. 工作区 ID 是否正确
4. 是否有足够的访问权限

### Q: 可以导出特定时间范围的对话吗？
A: 当前版本导出所有可访问的对话。您可以在导出后根据 CSV 文件中的时间字段进行筛选。

### Q: 导出的数据安全吗？
A: 所有数据都保存在您的本地计算机上，不会上传到任何第三方服务器。请妥善保管导出的文件。

### Q: 支持哪些数据格式？
A: 目前支持：
- JSON 格式（完整数据）
- CSV 格式（摘要信息）

## 🛠️ 故障排除

### 错误代码说明
- `401 Unauthorized`: API 密钥无效
- `403 Forbidden`: 权限不足
- `404 Not Found`: 工作区或对话不存在
- `429 Too Many Requests`: 请求过于频繁

### 解决方案
1. **API 密钥问题**: 检查密钥是否正确，是否过期
2. **权限问题**: 确认您的账户有访问相关数据的权限
3. **网络问题**: 检查网络连接，尝试使用 VPN
4. **频率限制**: 等待一段时间后重试

## 📞 获取帮助

如果遇到问题：
1. 查看错误信息和日志
2. 检查网络连接和权限设置
3. 联系 Dust.tt 支持：<EMAIL>
4. 查看 Dust.tt 官方文档

## 📝 注意事项

1. **数据隐私**: 导出的数据可能包含敏感信息，请妥善保管
2. **API 限制**: 遵守 Dust.tt 的 API 使用条款和频率限制
3. **存储空间**: 确保有足够的磁盘空间存储导出的数据
4. **备份**: 建议定期备份重要的对话数据

## 🔄 更新日志

- v2.0: 添加自动发现功能，改进用户体验
- v1.0: 基础导出功能

---

**祝您使用愉快！如果这个工具对您有帮助，请考虑分享给其他需要的用户。**

#!/usr/bin/env python3
"""
高级 Dust.tt 聊天记录导出工具

此工具尝试通过多种方法发现和导出聊天记录：
1. 通过工作区使用数据查找对话
2. 通过搜索 API 查找对话
3. 支持批量导出和格式化输出
"""

import requests
import json
import csv
import os
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import time
import argparse

class AdvancedDustExporter:
    def __init__(self, api_key: str, workspace_id: str):
        self.api_key = api_key
        self.workspace_id = workspace_id
        self.base_url = "https://dust.tt/api/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def search_workspace(self, query: str = "", limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索工作区内容，可能包含对话
        
        Args:
            query: 搜索查询
            limit: 结果限制
        """
        url = f"{self.base_url}/w/{self.workspace_id}/search"
        
        payload = {
            "query": query,
            "limit": limit,
            "filter": {
                "parents": {
                    "in": [],
                    "not": []
                }
            }
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            return response.json().get('nodes', [])
        except requests.exceptions.RequestException as e:
            print(f"搜索失败: {e}")
            return []
    
    def discover_conversations(self) -> List[str]:
        """
        尝试发现工作区中的对话 ID
        
        Returns:
            发现的对话 ID 列表
        """
        print("🔍 正在搜索工作区中的对话...")
        
        conversation_ids = set()
        
        # 方法1: 搜索常见的对话相关关键词
        search_terms = ["conversation", "chat", "message", "assistant", ""]
        
        for term in search_terms:
            print(f"搜索关键词: '{term}'")
            results = self.search_workspace(term, limit=50)
            
            for result in results:
                # 查找可能的对话 ID 模式
                text_content = json.dumps(result)
                # 查找 UUID 格式的 ID
                uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                matches = re.findall(uuid_pattern, text_content, re.IGNORECASE)
                
                for match in matches:
                    conversation_ids.add(match)
            
            time.sleep(0.5)  # 避免 API 限制
        
        # 方法2: 从工作区使用数据中查找
        print("📊 检查工作区使用数据...")
        usage_data = self.get_workspace_usage()
        if usage_data:
            usage_text = json.dumps(usage_data)
            uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
            matches = re.findall(uuid_pattern, usage_text, re.IGNORECASE)
            for match in matches:
                conversation_ids.add(match)
        
        conversation_list = list(conversation_ids)
        print(f"✅ 发现 {len(conversation_list)} 个可能的对话 ID")
        
        return conversation_list
    
    def validate_conversation_id(self, conversation_id: str) -> bool:
        """验证对话 ID 是否有效"""
        try:
            conversation = self.get_conversation(conversation_id)
            return bool(conversation)
        except:
            return False
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """获取对话摘要信息"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return {}
        
        # 提取关键信息
        summary = {
            "id": conversation_id,
            "title": conversation.get("title", "无标题"),
            "created": conversation.get("created", ""),
            "updated": conversation.get("updated", ""),
            "status": conversation.get("status", ""),
            "message_count": 0,
            "participants": []
        }
        
        # 计算消息数量
        if "content" in conversation:
            summary["message_count"] = len(conversation["content"])
        
        # 提取参与者
        if "participants" in conversation:
            summary["participants"] = conversation["participants"]
        
        return summary
    
    def export_all_conversations(self, output_dir: str = "dust_export"):
        """
        导出所有发现的对话
        
        Args:
            output_dir: 输出目录
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 发现对话
        conversation_ids = self.discover_conversations()
        
        if not conversation_ids:
            print("❌ 未发现任何对话")
            return
        
        # 验证对话 ID
        print("🔍 验证对话 ID...")
        valid_conversations = []
        
        for conv_id in conversation_ids:
            print(f"验证: {conv_id}")
            if self.validate_conversation_id(conv_id):
                valid_conversations.append(conv_id)
                print(f"✅ 有效")
            else:
                print(f"❌ 无效")
            time.sleep(0.3)
        
        if not valid_conversations:
            print("❌ 没有找到有效的对话")
            return
        
        print(f"\n📋 找到 {len(valid_conversations)} 个有效对话")
        
        # 创建摘要文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = os.path.join(output_dir, f"conversations_summary_{timestamp}.csv")
        
        with open(summary_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'conversation_id', 'title', 'created_at', 'updated_at',
                'message_count', 'participant_count', 'status'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            # 导出每个对话
            for i, conv_id in enumerate(valid_conversations, 1):
                print(f"\n📥 导出对话 {i}/{len(valid_conversations)}: {conv_id}")
                
                # 获取对话摘要
                summary = self.get_conversation_summary(conv_id)
                
                if summary:
                    # 写入 CSV
                    csv_row = {
                        'conversation_id': conv_id,
                        'title': summary.get('title', ''),
                        'created_at': summary.get('created', ''),
                        'updated_at': summary.get('updated', ''),
                        'message_count': summary.get('message_count', 0),
                        'participant_count': len(summary.get('participants', [])),
                        'status': summary.get('status', '')
                    }
                    writer.writerow(csv_row)
                    
                    # 导出完整对话数据
                    conv_file = os.path.join(output_dir, f"conversation_{conv_id}.json")
                    self.export_conversation_to_json(conv_id, conv_file)
                
                time.sleep(0.5)  # 避免 API 限制
        
        print(f"\n✅ 导出完成!")
        print(f"📁 输出目录: {output_dir}")
        print(f"📄 摘要文件: {summary_file}")
        print(f"💾 共导出 {len(valid_conversations)} 个对话")
    
    def get_workspace_usage(self) -> Dict[str, Any]:
        """获取工作区使用数据"""
        url = f"{self.base_url}/w/{self.workspace_id}/workspace-usage"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取工作区使用数据失败: {e}")
            return {}
    
    def get_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """获取单个对话详情"""
        url = f"{self.base_url}/w/{self.workspace_id}/assistant/conversations/{conversation_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {}
    
    def export_conversation_to_json(self, conversation_id: str, output_file: str):
        """导出单个对话到 JSON 文件"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return
        
        # 获取对话事件
        events = self.get_conversation_events(conversation_id)
        
        export_data = {
            "conversation": conversation,
            "events": events,
            "exported_at": datetime.now().isoformat(),
            "exporter_version": "2.0"
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    def get_conversation_events(self, conversation_id: str) -> List[Dict[str, Any]]:
        """获取对话事件流"""
        url = f"{self.base_url}/w/{self.workspace_id}/assistant/conversations/{conversation_id}/events"
        
        try:
            response = self.session.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            events = []
            for line in response.iter_lines():
                if line:
                    try:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            data = json.loads(line_str[6:])
                            events.append(data)
                    except json.JSONDecodeError:
                        continue
            
            return events
        except requests.exceptions.RequestException:
            return []


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="高级 Dust.tt 聊天记录导出工具")
    parser.add_argument("--workspace-id", required=True, help="工作区 ID")
    parser.add_argument("--output-dir", default="dust_export", help="输出目录")
    parser.add_argument("--api-key", default="sk-a07f7eb5929d04969c547fad85305bd2", help="API 密钥")
    
    args = parser.parse_args()
    
    print("=== 高级 Dust.tt 聊天记录导出工具 ===\n")
    
    # 创建导出器
    exporter = AdvancedDustExporter(args.api_key, args.workspace_id)
    
    # 测试连接
    print("🔗 测试 API 连接...")
    usage_data = exporter.get_workspace_usage()
    if not usage_data:
        print("❌ API 连接失败，请检查 API 密钥和工作区 ID")
        return
    
    print("✅ API 连接成功!")
    
    # 开始导出
    exporter.export_all_conversations(args.output_dir)


if __name__ == "__main__":
    main()

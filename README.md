# Dust.tt 聊天记录导出工具

这个工具集可以帮助您导出 Dust.tt 应用中的聊天记录和对话数据。

## 文件说明

### 1. `quick_start.py` - 🚀 一键启动脚本（推荐）
- 自动引导完整导出流程
- 集成工作区 ID 发现和对话导出
- 生成详细的导出报告
- 适合新手用户

### 2. `find_workspace_id.py` - 工作区 ID 发现工具
- 自动测试 API 连接
- 基于应用名称生成候选 ID
- 批量验证工作区 ID
- 保存有效配置供后续使用

### 3. `advanced_dust_exporter.py` - 高级导出工具
- 自动发现工作区中的对话
- 批量验证和导出所有对话
- 生成对话摘要 CSV 文件
- 更强的错误处理和重试机制

### 4. `dust_chat_exporter.py` - 基础导出工具
- 适用于已知对话 ID 的情况
- 支持单个对话导出
- 支持批量对话导出
- 可获取工作区使用数据

## 使用前准备

### 1. 获取必要信息
您已提供的信息：
- **API 密钥**: `sk-a07f7eb5929d04969c547fad85305bd2`
- **域名**: `https://dust.tt`
- **应用名称**: 笔记导出

### 2. 获取工作区 ID (wId)
工作区 ID 通常可以在以下位置找到：
- Dust.tt 网页 URL 中：`https://dust.tt/w/{wId}/...`
- 浏览器开发者工具的网络请求中
- 联系 Dust.tt 支持获取

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 🚀 方法一：一键快速启动（推荐新手）

```bash
python quick_start.py
```

这个脚本会自动引导您完成整个过程：
1. 自动查找工作区 ID
2. 导出所有聊天记录
3. 生成详细报告

### 方法二：手动查找工作区 ID

```bash
python find_workspace_id.py
```

这个工具会帮助您：
- 测试 API 连接
- 自动生成可能的工作区 ID
- 验证工作区 ID 有效性
- 保存配置供后续使用

### 方法三：使用基础工具

```bash
python dust_chat_exporter.py
```

然后按提示输入：
1. 工作区 ID
2. 选择导出方式
3. 输入对话 ID（如果需要）

### 方法四：使用高级工具

```bash
python advanced_dust_exporter.py --workspace-id YOUR_WORKSPACE_ID
```

可选参数：
- `--output-dir`: 指定输出目录（默认：dust_export）
- `--api-key`: 指定 API 密钥（已预设）

示例：
```bash
python advanced_dust_exporter.py --workspace-id abc123 --output-dir my_export
```

## 输出文件

### 高级工具输出：
- `conversations_summary_YYYYMMDD_HHMMSS.csv` - 所有对话的摘要信息
- `conversation_{ID}.json` - 每个对话的完整数据

### CSV 文件包含字段：
- conversation_id: 对话 ID
- title: 对话标题
- created_at: 创建时间
- updated_at: 更新时间
- message_count: 消息数量
- participant_count: 参与者数量
- status: 对话状态

### JSON 文件包含：
- conversation: 对话基本信息和消息内容
- events: 对话事件流
- exported_at: 导出时间
- exporter_version: 导出工具版本

## 获取工作区 ID 的方法

### 方法1：从浏览器 URL 获取
1. 登录 Dust.tt
2. 查看浏览器地址栏
3. URL 格式：`https://dust.tt/w/{wId}/...`
4. 复制 `/w/` 后面的部分

### 方法2：从开发者工具获取
1. 打开 Dust.tt 网页
2. 按 F12 打开开发者工具
3. 切换到 Network 标签
4. 刷新页面或进行操作
5. 查找包含 `/api/v1/w/` 的请求
6. 从 URL 中提取工作区 ID

### 方法3：使用测试脚本
创建一个简单的测试脚本来尝试不同的工作区 ID：

```python
import requests

api_key = "sk-a07f7eb5929d04969c547fad85305bd2"
possible_ids = ["your-workspace-id-here"]  # 添加可能的 ID

for wid in possible_ids:
    url = f"https://dust.tt/api/v1/w/{wid}/workspace-usage"
    headers = {"Authorization": f"Bearer {api_key}"}
    
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        print(f"✅ 找到有效的工作区 ID: {wid}")
        break
    else:
        print(f"❌ 无效的工作区 ID: {wid}")
```

## 注意事项

1. **API 限制**: Dust.tt 可能有 API 调用频率限制，工具已内置延迟机制
2. **权限要求**: 确保您的 API 密钥有访问对话数据的权限
3. **数据隐私**: 导出的数据可能包含敏感信息，请妥善保管
4. **网络连接**: 确保网络连接稳定，大量数据导出可能需要较长时间

## 故障排除

### 常见错误：

1. **401 Unauthorized**: API 密钥无效或过期
2. **403 Forbidden**: 没有访问权限
3. **404 Not Found**: 工作区 ID 或对话 ID 不存在
4. **429 Too Many Requests**: API 调用过于频繁

### 解决方案：

1. 检查 API 密钥是否正确
2. 确认工作区 ID 是否正确
3. 增加请求间隔时间
4. 联系 Dust.tt 支持获取帮助

## 联系支持

如果遇到问题，可以：
1. 检查 Dust.tt 官方文档
2. 联系 Dust.tt 支持：<EMAIL>
3. 查看 API 状态页面

## 版本历史

- v1.0: 基础导出功能
- v2.0: 高级自动发现功能，批量导出，改进错误处理

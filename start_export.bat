@echo off
chcp 65001 >nul
echo ========================================
echo    Dust.tt 聊天记录导出工具
echo ========================================
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Python，请先安装 Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python 已安装

REM 检查 requests 库
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少 requests 库，正在安装...
    pip install requests
    if errorlevel 1 (
        echo ❌ 安装 requests 库失败
        pause
        exit /b 1
    )
    echo ✅ requests 库安装成功
) else (
    echo ✅ requests 库已安装
)

echo.
echo 🔍 测试 API 连接...
python test_api.py
if errorlevel 1 (
    echo.
    echo ❌ API 连接测试失败，请检查网络和 API 密钥
    pause
    exit /b 1
)

echo.
echo 🚀 启动导出工具...
python quick_start.py

echo.
echo 导出完成！
pause

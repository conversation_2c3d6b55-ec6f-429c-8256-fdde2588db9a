#!/usr/bin/env python3
"""
Dust.tt 聊天记录导出工具

使用说明：
1. 确保您有有效的 API 密钥
2. 需要知道您的工作区 ID (wId)
3. 运行脚本导出聊天记录

注意：由于 Dust.tt API 限制，您可能需要手动获取对话 ID 列表
"""

import requests
import json
import csv
import os
from datetime import datetime
from typing import List, Dict, Any
import time

class DustChatExporter:
    def __init__(self, api_key: str, workspace_id: str):
        """
        初始化导出器
        
        Args:
            api_key: Dust.tt API 密钥 (sk-开头)
            workspace_id: 工作区 ID
        """
        self.api_key = api_key
        self.workspace_id = workspace_id
        self.base_url = "https://dust.tt/api/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def get_workspace_usage(self) -> Dict[str, Any]:
        """获取工作区使用数据"""
        url = f"{self.base_url}/w/{self.workspace_id}/workspace-usage"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取工作区使用数据失败: {e}")
            return {}
    
    def get_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """
        获取单个对话详情
        
        Args:
            conversation_id: 对话 ID
            
        Returns:
            对话数据字典
        """
        url = f"{self.base_url}/w/{self.workspace_id}/assistant/conversations/{conversation_id}"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取对话 {conversation_id} 失败: {e}")
            return {}
    
    def get_conversation_events(self, conversation_id: str) -> List[Dict[str, Any]]:
        """
        获取对话事件流
        
        Args:
            conversation_id: 对话 ID
            
        Returns:
            事件列表
        """
        url = f"{self.base_url}/w/{self.workspace_id}/assistant/conversations/{conversation_id}/events"
        
        try:
            response = requests.get(url, headers=self.headers, stream=True)
            response.raise_for_status()
            
            events = []
            for line in response.iter_lines():
                if line:
                    try:
                        # 处理 Server-Sent Events 格式
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            data = json.loads(line_str[6:])
                            events.append(data)
                    except json.JSONDecodeError:
                        continue
            
            return events
        except requests.exceptions.RequestException as e:
            print(f"获取对话事件 {conversation_id} 失败: {e}")
            return []
    
    def export_conversation_to_json(self, conversation_id: str, output_file: str):
        """
        导出单个对话到 JSON 文件
        
        Args:
            conversation_id: 对话 ID
            output_file: 输出文件路径
        """
        print(f"正在导出对话 {conversation_id}...")
        
        # 获取对话基本信息
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            print(f"无法获取对话 {conversation_id}")
            return
        
        # 获取对话事件
        events = self.get_conversation_events(conversation_id)
        
        # 合并数据
        export_data = {
            "conversation": conversation,
            "events": events,
            "exported_at": datetime.now().isoformat(),
            "exporter_version": "1.0"
        }
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"对话已导出到: {output_file}")
    
    def export_conversations_to_csv(self, conversation_ids: List[str], output_file: str):
        """
        导出多个对话到 CSV 文件
        
        Args:
            conversation_ids: 对话 ID 列表
            output_file: 输出文件路径
        """
        print(f"正在导出 {len(conversation_ids)} 个对话到 CSV...")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'conversation_id', 'title', 'created_at', 'updated_at',
                'message_count', 'participant_count', 'status'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for conv_id in conversation_ids:
                print(f"处理对话: {conv_id}")
                conversation = self.get_conversation(conv_id)
                
                if conversation:
                    row = {
                        'conversation_id': conv_id,
                        'title': conversation.get('title', ''),
                        'created_at': conversation.get('created', ''),
                        'updated_at': conversation.get('updated', ''),
                        'message_count': len(conversation.get('content', [])),
                        'participant_count': len(conversation.get('participants', [])),
                        'status': conversation.get('status', '')
                    }
                    writer.writerow(row)
                
                # 添加延迟避免 API 限制
                time.sleep(0.5)
        
        print(f"CSV 导出完成: {output_file}")
    
    def test_connection(self) -> bool:
        """测试 API 连接"""
        print("测试 API 连接...")
        
        try:
            usage_data = self.get_workspace_usage()
            if usage_data:
                print("✅ API 连接成功!")
                print(f"工作区 ID: {self.workspace_id}")
                return True
            else:
                print("❌ API 连接失败")
                return False
        except Exception as e:
            print(f"❌ API 连接错误: {e}")
            return False


def main():
    """主函数"""
    print("=== Dust.tt 聊天记录导出工具 ===\n")
    
    # 配置信息
    API_KEY = "sk-a07f7eb5929d04969c547fad85305bd2"
    WORKSPACE_ID = input("请输入您的工作区 ID (wId): ").strip()
    
    if not WORKSPACE_ID:
        print("❌ 工作区 ID 不能为空")
        return
    
    # 创建导出器
    exporter = DustChatExporter(API_KEY, WORKSPACE_ID)
    
    # 测试连接
    if not exporter.test_connection():
        return
    
    print("\n选择导出方式:")
    print("1. 导出单个对话 (需要对话 ID)")
    print("2. 导出多个对话 (需要对话 ID 列表)")
    print("3. 获取工作区使用数据")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        conv_id = input("请输入对话 ID: ").strip()
        if conv_id:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"conversation_{conv_id}_{timestamp}.json"
            exporter.export_conversation_to_json(conv_id, output_file)
    
    elif choice == "2":
        print("请输入对话 ID 列表 (每行一个，输入空行结束):")
        conversation_ids = []
        while True:
            conv_id = input().strip()
            if not conv_id:
                break
            conversation_ids.append(conv_id)
        
        if conversation_ids:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"conversations_{timestamp}.csv"
            exporter.export_conversations_to_csv(conversation_ids, output_file)
    
    elif choice == "3":
        usage_data = exporter.get_workspace_usage()
        if usage_data:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"workspace_usage_{timestamp}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(usage_data, f, ensure_ascii=False, indent=2)
            print(f"工作区使用数据已导出到: {output_file}")
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()

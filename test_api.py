#!/usr/bin/env python3
"""
Dust.tt API 连接测试脚本

快速测试您的 API 密钥是否有效
"""

import requests
import json

def test_api_connection():
    """测试 API 连接"""
    api_key = "sk-a07f7eb5929d04969c547fad85305bd2"
    
    print("🔑 测试 Dust.tt API 连接...")
    print(f"API 密钥: {api_key[:10]}...{api_key[-4:]}")
    
    # 测试一个无效的工作区 ID，主要是为了验证 API 密钥
    test_url = "https://dust.tt/api/v1/w/test-invalid-workspace/workspace-usage"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(test_url, headers=headers, timeout=10)
        
        if response.status_code == 401:
            print("❌ API 密钥无效或过期")
            print("请检查您的 API 密钥是否正确")
            return False
        elif response.status_code == 404:
            print("✅ API 密钥有效!")
            print("(404 错误是预期的，因为我们使用了无效的工作区 ID)")
            return True
        elif response.status_code == 403:
            print("⚠️ API 密钥有效，但权限不足")
            print("请确保您的 API 密钥有访问对话数据的权限")
            return True
        else:
            print(f"🤔 意外的响应状态: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return True
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络连接错误: {e}")
        return False

def main():
    print("=== Dust.tt API 连接测试 ===\n")
    
    # 检查 requests 库
    try:
        import requests
        print("✅ requests 库已安装")
    except ImportError:
        print("❌ 缺少 requests 库")
        print("请运行: pip install requests")
        return
    
    # 测试 API 连接
    if test_api_connection():
        print("\n🎉 API 连接测试通过!")
        print("您现在可以运行以下命令开始导出:")
        print("python quick_start.py")
    else:
        print("\n❌ API 连接测试失败")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. API 密钥是否正确")
        print("3. 是否有访问权限")

if __name__ == "__main__":
    main()

#!/bin/bash

echo "========================================"
echo "    Dust.tt 聊天记录导出工具"
echo "========================================"
echo

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 未找到 Python，请先安装 Python"
        echo "macOS: brew install python"
        echo "Ubuntu/Debian: sudo apt install python3"
        echo "CentOS/RHEL: sudo yum install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python 已安装 ($PYTHON_CMD)"

# 检查 pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "❌ 未找到 pip，请先安装 pip"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

# 检查 requests 库
$PYTHON_CMD -c "import requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少 requests 库，正在安装..."
    $PIP_CMD install requests
    if [ $? -ne 0 ]; then
        echo "❌ 安装 requests 库失败"
        exit 1
    fi
    echo "✅ requests 库安装成功"
else
    echo "✅ requests 库已安装"
fi

echo
echo "🔍 测试 API 连接..."
$PYTHON_CMD test_api.py
if [ $? -ne 0 ]; then
    echo
    echo "❌ API 连接测试失败，请检查网络和 API 密钥"
    exit 1
fi

echo
echo "🚀 启动导出工具..."
$PYTHON_CMD quick_start.py

echo
echo "导出完成！"

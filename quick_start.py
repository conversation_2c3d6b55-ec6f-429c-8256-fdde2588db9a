#!/usr/bin/env python3
"""
Dust.tt 聊天记录导出 - 快速启动脚本

这个脚本将引导您完成整个导出过程：
1. 查找工作区 ID
2. 导出聊天记录
3. 生成报告
"""

import os
import sys
import json
import subprocess
from datetime import datetime

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import requests
        print("✅ requests 库已安装")
    except ImportError:
        print("❌ 缺少 requests 库")
        print("请运行: pip install requests")
        return False
    
    return True

def load_saved_config():
    """加载已保存的配置"""
    config_file = "dust_workspace_config.json"
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            workspace_id = config.get("workspace_id")
            if workspace_id:
                print(f"✅ 找到已保存的工作区 ID: {workspace_id}")
                return workspace_id
        except Exception as e:
            print(f"⚠️ 读取配置文件失败: {e}")
    
    return None

def find_workspace_id():
    """查找工作区 ID"""
    print("\n🔍 查找工作区 ID...")
    
    # 检查是否有已保存的配置
    saved_id = load_saved_config()
    if saved_id:
        use_saved = input(f"是否使用已保存的工作区 ID '{saved_id}'? (y/n): ").strip().lower()
        if use_saved == 'y':
            return saved_id
    
    print("启动工作区 ID 发现工具...")
    
    try:
        # 运行工作区 ID 发现工具
        result = subprocess.run([sys.executable, "find_workspace_id.py"], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            # 尝试重新加载配置
            return load_saved_config()
        else:
            print("❌ 工作区 ID 发现失败")
            return None
            
    except Exception as e:
        print(f"❌ 运行工作区 ID 发现工具失败: {e}")
        return None

def export_conversations(workspace_id: str):
    """导出对话"""
    print(f"\n📥 开始导出工作区 '{workspace_id}' 的聊天记录...")
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"dust_export_{timestamp}"
    
    try:
        # 运行高级导出工具
        cmd = [sys.executable, "advanced_dust_exporter.py", 
               "--workspace-id", workspace_id,
               "--output-dir", output_dir]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ 导出完成! 输出目录: {output_dir}")
            return output_dir
        else:
            print("❌ 导出失败")
            return None
            
    except Exception as e:
        print(f"❌ 运行导出工具失败: {e}")
        return None

def generate_report(output_dir: str):
    """生成导出报告"""
    if not output_dir or not os.path.exists(output_dir):
        return
    
    print(f"\n📊 生成导出报告...")
    
    try:
        # 统计导出的文件
        files = os.listdir(output_dir)
        json_files = [f for f in files if f.endswith('.json') and f.startswith('conversation_')]
        csv_files = [f for f in files if f.endswith('.csv')]
        
        report = {
            "export_time": datetime.now().isoformat(),
            "output_directory": output_dir,
            "total_files": len(files),
            "conversation_files": len(json_files),
            "summary_files": len(csv_files),
            "file_list": files
        }
        
        # 保存报告
        report_file = os.path.join(output_dir, "export_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 显示报告
        print(f"📁 输出目录: {output_dir}")
        print(f"📄 总文件数: {len(files)}")
        print(f"💬 对话文件数: {len(json_files)}")
        print(f"📋 摘要文件数: {len(csv_files)}")
        print(f"📊 报告文件: {report_file}")
        
        # 显示文件列表
        if files:
            print("\n📋 导出的文件:")
            for file in sorted(files):
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"  - {file} ({file_size:,} bytes)")
        
    except Exception as e:
        print(f"⚠️ 生成报告失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 Dust.tt 聊天记录导出 - 快速启动")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查必要文件
    required_files = [
        "find_workspace_id.py",
        "advanced_dust_exporter.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    print("✅ 所有必要文件都存在")
    
    # 步骤1: 查找工作区 ID
    workspace_id = find_workspace_id()
    if not workspace_id:
        print("❌ 无法获取工作区 ID，导出终止")
        return
    
    # 步骤2: 导出对话
    output_dir = export_conversations(workspace_id)
    if not output_dir:
        print("❌ 导出失败")
        return
    
    # 步骤3: 生成报告
    generate_report(output_dir)
    
    print("\n🎉 导出流程完成!")
    print("\n📋 后续步骤:")
    print("1. 查看导出的文件")
    print("2. 检查 CSV 摘要文件了解对话概况")
    print("3. 查看 JSON 文件获取详细对话内容")
    print("4. 妥善保管导出的数据")
    
    # 询问是否打开输出目录
    if output_dir and os.path.exists(output_dir):
        open_dir = input(f"\n是否打开输出目录 '{output_dir}'? (y/n): ").strip().lower()
        if open_dir == 'y':
            try:
                import platform
                system = platform.system()
                
                if system == "Darwin":  # macOS
                    subprocess.run(["open", output_dir])
                elif system == "Windows":
                    subprocess.run(["explorer", output_dir])
                elif system == "Linux":
                    subprocess.run(["xdg-open", output_dir])
                else:
                    print(f"请手动打开目录: {output_dir}")
                    
            except Exception as e:
                print(f"⚠️ 无法自动打开目录: {e}")
                print(f"请手动打开目录: {output_dir}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Dust.tt 工作区 ID 发现工具

此工具帮助您找到正确的工作区 ID
"""

import requests
import json
import re
from typing import List, Optional

class WorkspaceIDFinder:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://dust.tt/api/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def test_workspace_id(self, workspace_id: str) -> dict:
        """
        测试工作区 ID 是否有效
        
        Args:
            workspace_id: 要测试的工作区 ID
            
        Returns:
            包含测试结果的字典
        """
        result = {
            "workspace_id": workspace_id,
            "valid": False,
            "error": None,
            "data": None
        }
        
        # 测试工作区使用数据端点
        url = f"{self.base_url}/w/{workspace_id}/workspace-usage"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                result["valid"] = True
                result["data"] = response.json()
                print(f"✅ 工作区 ID '{workspace_id}' 有效!")
            elif response.status_code == 401:
                result["error"] = "API 密钥无效或过期"
                print(f"❌ API 密钥问题: {workspace_id}")
            elif response.status_code == 403:
                result["error"] = "没有访问权限"
                print(f"❌ 权限不足: {workspace_id}")
            elif response.status_code == 404:
                result["error"] = "工作区不存在"
                print(f"❌ 工作区不存在: {workspace_id}")
            else:
                result["error"] = f"HTTP {response.status_code}: {response.text}"
                print(f"❌ 错误 {response.status_code}: {workspace_id}")
                
        except requests.exceptions.RequestException as e:
            result["error"] = str(e)
            print(f"❌ 网络错误: {workspace_id} - {e}")
        
        return result
    
    def try_common_patterns(self, base_name: str) -> List[str]:
        """
        基于应用名称生成可能的工作区 ID 模式
        
        Args:
            base_name: 基础名称（如应用名称）
            
        Returns:
            可能的工作区 ID 列表
        """
        patterns = []
        
        # 清理基础名称
        clean_name = re.sub(r'[^\w\-]', '', base_name.lower())
        
        # 常见模式
        patterns.extend([
            clean_name,
            f"{clean_name}-workspace",
            f"{clean_name}-ws",
            f"ws-{clean_name}",
            f"{clean_name}123",
            f"{clean_name}-1",
            f"my-{clean_name}",
            f"{clean_name}-app",
            f"app-{clean_name}",
        ])
        
        # 如果是中文，尝试拼音或英文翻译
        if "笔记" in base_name:
            patterns.extend([
                "notes", "note", "biji", "notebook", "notes-app",
                "note-export", "export-notes", "my-notes"
            ])
        
        if "导出" in base_name:
            patterns.extend([
                "export", "exporter", "data-export", "export-tool"
            ])
        
        return list(set(patterns))  # 去重
    
    def interactive_search(self):
        """交互式搜索工作区 ID"""
        print("=== Dust.tt 工作区 ID 发现工具 ===\n")
        
        # 首先测试 API 密钥
        print("🔑 测试 API 密钥...")
        test_result = self.test_workspace_id("test-invalid-id")
        
        if "API 密钥无效" in str(test_result.get("error", "")):
            print("❌ API 密钥无效，请检查您的密钥")
            return
        
        print("✅ API 密钥有效\n")
        
        while True:
            print("选择搜索方式:")
            print("1. 手动输入工作区 ID")
            print("2. 基于应用名称自动生成候选 ID")
            print("3. 批量测试 ID 列表")
            print("4. 退出")
            
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == "1":
                self.manual_input()
            elif choice == "2":
                self.auto_generate()
            elif choice == "3":
                self.batch_test()
            elif choice == "4":
                break
            else:
                print("❌ 无效选择\n")
    
    def manual_input(self):
        """手动输入工作区 ID"""
        print("\n--- 手动输入模式 ---")
        
        while True:
            workspace_id = input("请输入工作区 ID (输入 'back' 返回): ").strip()
            
            if workspace_id.lower() == 'back':
                break
            
            if not workspace_id:
                print("❌ 工作区 ID 不能为空")
                continue
            
            result = self.test_workspace_id(workspace_id)
            
            if result["valid"]:
                print(f"\n🎉 找到有效的工作区 ID: {workspace_id}")
                self.show_workspace_info(result["data"])
                
                save = input("\n是否保存此工作区 ID 到文件? (y/n): ").strip().lower()
                if save == 'y':
                    self.save_workspace_id(workspace_id, result["data"])
                break
            else:
                print(f"错误: {result['error']}\n")
    
    def auto_generate(self):
        """基于应用名称自动生成候选 ID"""
        print("\n--- 自动生成模式 ---")
        
        app_name = input("请输入您的应用名称 (如: 笔记导出): ").strip()
        
        if not app_name:
            print("❌ 应用名称不能为空")
            return
        
        print(f"\n🔍 基于 '{app_name}' 生成候选工作区 ID...")
        
        candidates = self.try_common_patterns(app_name)
        print(f"生成了 {len(candidates)} 个候选 ID")
        
        valid_ids = []
        
        for i, candidate in enumerate(candidates, 1):
            print(f"测试 {i}/{len(candidates)}: {candidate}")
            result = self.test_workspace_id(candidate)
            
            if result["valid"]:
                valid_ids.append((candidate, result["data"]))
        
        if valid_ids:
            print(f"\n🎉 找到 {len(valid_ids)} 个有效的工作区 ID:")
            for i, (wid, data) in enumerate(valid_ids, 1):
                print(f"{i}. {wid}")
            
            if len(valid_ids) == 1:
                workspace_id, data = valid_ids[0]
                print(f"\n使用工作区 ID: {workspace_id}")
                self.show_workspace_info(data)
                
                save = input("\n是否保存此工作区 ID 到文件? (y/n): ").strip().lower()
                if save == 'y':
                    self.save_workspace_id(workspace_id, data)
            else:
                choice = input(f"\n请选择要使用的工作区 (1-{len(valid_ids)}): ").strip()
                try:
                    idx = int(choice) - 1
                    if 0 <= idx < len(valid_ids):
                        workspace_id, data = valid_ids[idx]
                        print(f"\n使用工作区 ID: {workspace_id}")
                        self.show_workspace_info(data)
                        
                        save = input("\n是否保存此工作区 ID 到文件? (y/n): ").strip().lower()
                        if save == 'y':
                            self.save_workspace_id(workspace_id, data)
                except ValueError:
                    print("❌ 无效选择")
        else:
            print("\n❌ 未找到有效的工作区 ID")
            print("建议:")
            print("1. 检查应用名称是否正确")
            print("2. 尝试手动输入模式")
            print("3. 联系 Dust.tt 支持获取正确的工作区 ID")
    
    def batch_test(self):
        """批量测试 ID 列表"""
        print("\n--- 批量测试模式 ---")
        print("请输入要测试的工作区 ID 列表 (每行一个，输入空行结束):")
        
        ids_to_test = []
        while True:
            workspace_id = input().strip()
            if not workspace_id:
                break
            ids_to_test.append(workspace_id)
        
        if not ids_to_test:
            print("❌ 没有输入任何 ID")
            return
        
        print(f"\n🔍 测试 {len(ids_to_test)} 个工作区 ID...")
        
        valid_ids = []
        for i, workspace_id in enumerate(ids_to_test, 1):
            print(f"测试 {i}/{len(ids_to_test)}: {workspace_id}")
            result = self.test_workspace_id(workspace_id)
            
            if result["valid"]:
                valid_ids.append((workspace_id, result["data"]))
        
        if valid_ids:
            print(f"\n🎉 找到 {len(valid_ids)} 个有效的工作区 ID:")
            for workspace_id, data in valid_ids:
                print(f"- {workspace_id}")
        else:
            print("\n❌ 没有找到有效的工作区 ID")
    
    def show_workspace_info(self, data: dict):
        """显示工作区信息"""
        if not data:
            return
        
        print("\n📊 工作区信息:")
        print(f"- 数据类型: {type(data).__name__}")
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (str, int, float, bool)):
                    print(f"- {key}: {value}")
                elif isinstance(value, (list, dict)):
                    print(f"- {key}: {type(value).__name__} (长度: {len(value)})")
    
    def save_workspace_id(self, workspace_id: str, data: dict):
        """保存工作区 ID 到文件"""
        config = {
            "workspace_id": workspace_id,
            "api_key": self.api_key,
            "discovered_at": "2025-01-27",
            "workspace_data": data
        }
        
        filename = "dust_workspace_config.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 工作区配置已保存到: {filename}")
        print(f"现在您可以使用以下命令导出聊天记录:")
        print(f"python advanced_dust_exporter.py --workspace-id {workspace_id}")


def main():
    api_key = "sk-a07f7eb5929d04969c547fad85305bd2"
    finder = WorkspaceIDFinder(api_key)
    finder.interactive_search()


if __name__ == "__main__":
    main()
